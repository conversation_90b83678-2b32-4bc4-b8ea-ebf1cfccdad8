const { spawn } = require('child_process');

const TOKEN_ADDRESS = 'Aj27AMdcxtKvmuhbM2D6wcSWmFGCo8bST7g5BYDNBAGS';

class TokenWhaleAnalyzer {
    constructor() {
        this.mcpProcess = null;
        this.requestId = 1;
    }

    async startMCPServer() {
        this.mcpProcess = spawn('cmd', ['/c', 'npx', '@moralisweb3/api-mcp-server', '--transport', 'stdio'], {
            env: {
                ...process.env,
                MORALIS_API_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJub25jZSI6ImNjM2NlNjMxLTU3MmYtNDY4NC1hZTYwLTE3Y2Y1YmMyZTAyZSIsIm9yZ0lkIjoiNDYyMzg4IiwidXNlcklkIjoiNDc1Njk4IiwidHlwZUlkIjoiZDc3Zjg4NTctZTA4Zi00M2I3LWIzMGEtZDI3OGRkNmM0NTNkIiwidHlwZSI6IlBST0pFQ1QiLCJpYXQiOjE3NTM4ODE2MDAsImV4cCI6NDkwOTY0MTYwMH0.gmNIVS5dgAnOscm2CLKQstXxPvj4tK_zK1IeDGze-Co'
            },
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true
        });

        await new Promise((resolve) => setTimeout(resolve, 2000));
    }

    async sendMCPRequest(method, params = {}) {
        return new Promise((resolve, reject) => {
            const request = {
                jsonrpc: '2.0',
                id: this.requestId++,
                method: method,
                params: params
            };

            this.mcpProcess.stdin.write(JSON.stringify(request) + '\n');

            const timeout = setTimeout(() => {
                reject(new Error('Request timeout'));
            }, 30000);

            const onData = (data) => {
                clearTimeout(timeout);
                try {
                    const response = JSON.parse(data.toString());
                    if (response.error) {
                        reject(new Error(response.error.message));
                    } else {
                        resolve(response.result);
                    }
                } catch (error) {
                    resolve({ rawData: data.toString() });
                }
                this.mcpProcess.stdout.off('data', onData);
            };

            this.mcpProcess.stdout.on('data', onData);
        });
    }

    async getTokenSwaps() {
        try {
            // Try different methods to get token swap data
            const methods = [
                'solana_getswapsbytokenaddress',
                'solana_gettopholders',
                'solana_gettokenholders'
            ];

            for (const method of methods) {
                try {
                    const result = await this.sendMCPRequest('tools/call', {
                        name: method,
                        arguments: {
                            network: 'mainnet',
                            address: TOKEN_ADDRESS,
                            limit: 100
                        }
                    });

                    if (result && result.content) {
                        return { method, data: result };
                    }
                } catch (error) {
                    continue;
                }
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    async analyzeWhales() {
        try {
            await this.startMCPServer();

            const result = await this.getTokenSwaps();

            if (!result) {
                return "Could not retrieve token data";
            }

            const responseText = result.data.content[0].text;

            // Try to parse and analyze the data
            try {
                const data = JSON.parse(responseText);

                if (result.method === 'solana_getswapsbytokenaddress' && data.result) {
                    // Analyze swap data for recent large buys
                    const now = new Date();
                    const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

                    const recentBuys = data.result.filter(swap => {
                        const swapTime = new Date(swap.blockTimestamp);
                        return swapTime >= twentyFourHoursAgo &&
                               swap.transactionType === 'buy' &&
                               parseFloat(swap.totalValueUsd || 0) >= 5000;
                    });

                    const whaleWallets = new Set(recentBuys.map(buy => buy.walletAddress));
                    return `${whaleWallets.size} wallets with $5k+ purchases in last 24h`;

                } else if (result.method === 'solana_gettopholders' && data.length) {
                    // Analyze top holders
                    const largeHolders = data.filter(holder =>
                        parseFloat(holder.usdValue || 0) >= 5000
                    );
                    return `${largeHolders.length} wallets currently hold $5k+ worth (but can't determine 24h purchases from this data)`;

                } else {
                    return `Got data via ${result.method} but couldn't analyze for 24h $5k+ purchases`;
                }

            } catch (parseError) {
                return `Raw response from ${result.method}: ${responseText.substring(0, 200)}...`;
            }

        } catch (error) {
            return `Error: ${error.message}`;
        } finally {
            if (this.mcpProcess) {
                this.mcpProcess.kill();
            }
        }
    }
}

async function main() {
    const analyzer = new TokenWhaleAnalyzer();
    const result = await analyzer.analyzeWhales();
    console.log(result);
}

main().catch(console.error);
