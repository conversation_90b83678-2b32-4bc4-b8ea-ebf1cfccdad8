const axios = require('axios');

const WALLET_ADDRESS = 'Fn3UVQzeA5XDJXvVPnoZnYiUSEmBkU5MAzM3YeU54Mhu';

// Multiple API approaches
class MultiAPIAnalyzer {
    constructor() {
        this.apis = [
            {
                name: 'He<PERSON>',
                getTransactions: this.getHeliusTransactions.bind(this),
                parseTransaction: this.parseHeliusTransaction.bind(this)
            },
            {
                name: 'Solscan',
                getTransactions: this.getSolscanTransactions.bind(this),
                parseTransaction: this.parseSolscanTransaction.bind(this)
            },
            {
                name: 'SolanaFM',
                getTransactions: this.getSolanaFMTransactions.bind(this),
                parseTransaction: this.parseSolanaFMTransaction.bind(this)
            }
        ];
    }

    async getHeliusTransactions() {
        try {
            console.log('🔄 Trying Helius API...');
            
            // Try Helius free endpoint
            const response = await axios.post('https://api.helius.xyz/v0/addresses/transactions', {
                addresses: [WALLET_ADDRESS],
                limit: 20
            }, {
                timeout: 10000
            });
            
            console.log('✅ Helius API success!');
            return response.data;
        } catch (error) {
            console.log('❌ Helius API failed:', error.response?.data?.message || error.message);
            return null;
        }
    }

    async getSolscanTransactions() {
        try {
            console.log('🔄 Trying Solscan API...');
            
            const response = await axios.get(`https://public-api.solscan.io/account/transactions`, {
                params: {
                    account: WALLET_ADDRESS,
                    limit: 20
                },
                timeout: 10000
            });
            
            console.log('✅ Solscan API success!');
            return response.data;
        } catch (error) {
            console.log('❌ Solscan API failed:', error.response?.data?.message || error.message);
            return null;
        }
    }

    async getSolanaFMTransactions() {
        try {
            console.log('🔄 Trying SolanaFM API...');
            
            const response = await axios.get(`https://api.solana.fm/v1/addresses/${WALLET_ADDRESS}/transactions`, {
                params: {
                    limit: 20
                },
                timeout: 10000
            });
            
            console.log('✅ SolanaFM API success!');
            return response.data;
        } catch (error) {
            console.log('❌ SolanaFM API failed:', error.response?.data?.message || error.message);
            return null;
        }
    }

    parseHeliusTransaction(tx) {
        return {
            signature: tx.signature,
            blockTime: tx.blockTime,
            slot: tx.slot,
            fee: tx.fee,
            success: !tx.err,
            type: tx.type || 'unknown',
            description: tx.description || '',
            source: 'Helius'
        };
    }

    parseSolscanTransaction(tx) {
        return {
            signature: tx.txHash,
            blockTime: tx.blockTime,
            slot: tx.slot,
            fee: tx.fee,
            success: tx.status === 'Success',
            type: 'transaction',
            description: '',
            source: 'Solscan'
        };
    }

    parseSolanaFMTransaction(tx) {
        return {
            signature: tx.transactionHash,
            blockTime: tx.blockTime,
            slot: tx.slot,
            fee: tx.fee,
            success: tx.result === 'success',
            type: 'transaction',
            description: '',
            source: 'SolanaFM'
        };
    }

    async analyzeWallet() {
        console.log(`\n🔍 Multi-API Wallet Analysis: ${WALLET_ADDRESS}\n`);
        
        for (const api of this.apis) {
            try {
                console.log(`\n📡 Trying ${api.name} API...`);
                
                const transactions = await api.getTransactions();
                
                if (transactions && transactions.length > 0) {
                    console.log(`✅ Got ${transactions.length} transactions from ${api.name}`);
                    
                    console.log(`\n📋 LAST 20 TRANSACTIONS (via ${api.name}):`);
                    console.log('='.repeat(80));
                    
                    transactions.slice(0, 20).forEach((tx, index) => {
                        const parsed = api.parseTransaction(tx);
                        const date = parsed.blockTime ? new Date(parsed.blockTime * 1000).toLocaleString() : 'Unknown';
                        const status = parsed.success ? '✅' : '❌';
                        
                        console.log(`\n${index + 1}. ${status} ${parsed.signature?.substring(0, 30) || 'Unknown'}...`);
                        console.log(`   Date: ${date}`);
                        console.log(`   Fee: ${parsed.fee ? (parsed.fee / 1e9).toFixed(6) : 'Unknown'} SOL`);
                        console.log(`   Type: ${parsed.type}`);
                        if (parsed.description) {
                            console.log(`   Description: ${parsed.description}`);
                        }
                        console.log(`   Source: ${parsed.source}`);
                    });
                    
                    return; // Success, exit
                }
                
            } catch (error) {
                console.log(`❌ ${api.name} failed:`, error.message);
            }
        }
        
        // If all APIs failed, try a simple approach
        await this.trySimpleApproach();
    }

    async trySimpleApproach() {
        console.log('\n🔄 Trying simple HTTP approach...');
        
        const endpoints = [
            `https://api.solscan.io/account/transactions?account=${WALLET_ADDRESS}&limit=20`,
            `https://public-api.solscan.io/account/transactions?account=${WALLET_ADDRESS}&limit=20`,
            `https://api.solana.fm/v1/addresses/${WALLET_ADDRESS}/transactions?limit=20`
        ];
        
        for (const endpoint of endpoints) {
            try {
                console.log(`🔄 Trying: ${endpoint}`);
                
                const response = await axios.get(endpoint, {
                    timeout: 15000,
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });
                
                console.log('✅ Success! Raw response:');
                console.log(JSON.stringify(response.data, null, 2));
                return;
                
            } catch (error) {
                console.log(`❌ Failed: ${error.response?.status} ${error.response?.statusText || error.message}`);
            }
        }
        
        console.log('\n⚠️  All approaches failed. This could be due to:');
        console.log('   - API rate limits');
        console.log('   - Wallet having no recent transactions');
        console.log('   - API access restrictions');
        console.log('   - Network issues');
    }
}

// Run the analysis
async function main() {
    const analyzer = new MultiAPIAnalyzer();
    await analyzer.analyzeWallet();
}

main().catch(console.error);
