const axios = require('axios');

const MORALIS_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJub25jZSI6ImNjM2NlNjMxLTU3MmYtNDY4NC1hZTYwLTE3Y2Y1YmMyZTAyZSIsIm9yZ0lkIjoiNDYyMzg4IiwidXNlcklkIjoiNDc1Njk4IiwidHlwZUlkIjoiZDc3Zjg4NTctZTA4Zi00M2I3LWIzMGEtZDI3OGRkNmM0NTNkIiwidHlwZSI6IlBST0pFQ1QiLCJpYXQiOjE3NTM4ODE2MDAsImV4cCI6NDkwOTY0MTYwMH0.gmNIVS5dgAnOscm2CLKQstXxPvj4tK_zK1IeDGze-Co';
const WALLET_ADDRESS = 'Fn3UVQzeA5XDJXvVPnoZnYiUSEmBkU5MAzM3YeU54Mhu';

const moralisConfig = {
    headers: {
        'X-API-Key': MORALIS_API_KEY,
        'Content-Type': 'application/json'
    }
};

async function getWalletInfo() {
    console.log(`\n🔍 Analyzing Wallet: ${WALLET_ADDRESS}\n`);
    
    try {
        // Get wallet balance and basic info
        console.log('📊 Getting wallet balance...');
        const balanceResponse = await axios.get(
            `https://solana-gateway.moralis.io/account/mainnet/${WALLET_ADDRESS}/balance`,
            moralisConfig
        );
        
        console.log('💰 Wallet Balance:');
        console.log(`   SOL Balance: ${balanceResponse.data.solana / 1e9} SOL`);
        
        // Get wallet tokens
        console.log('\n🪙 Getting wallet tokens...');
        const tokensResponse = await axios.get(
            `https://solana-gateway.moralis.io/account/mainnet/${WALLET_ADDRESS}/tokens`,
            moralisConfig
        );
        
        console.log('\n🏆 Top 10 Tokens:');
        const tokens = tokensResponse.data.slice(0, 10);
        tokens.forEach((token, index) => {
            const amount = token.amount / Math.pow(10, token.decimals);
            console.log(`   ${index + 1}. ${token.symbol || 'Unknown'} (${token.mint})`);
            console.log(`      Amount: ${amount.toFixed(6)}`);
            console.log(`      Decimals: ${token.decimals}`);
            console.log('');
        });
        
        // Get wallet transactions (last 20)
        console.log('📈 Getting recent transactions...');
        const transactionsResponse = await axios.get(
            `https://solana-gateway.moralis.io/account/mainnet/${WALLET_ADDRESS}/transactions?limit=20`,
            moralisConfig
        );
        
        console.log('\n📋 Last 20 Transactions:');
        transactionsResponse.data.forEach((tx, index) => {
            console.log(`   ${index + 1}. Signature: ${tx.signatures[0]}`);
            console.log(`      Block Time: ${new Date(tx.blockTime * 1000).toLocaleString()}`);
            console.log(`      Slot: ${tx.slot}`);
            console.log(`      Fee: ${tx.meta.fee / 1e9} SOL`);
            console.log(`      Status: ${tx.meta.err ? 'Failed' : 'Success'}`);
            console.log('');
        });
        
    } catch (error) {
        console.error('❌ Error fetching wallet data:', error.response?.data || error.message);
        
        // Try alternative endpoints if the first ones fail
        console.log('\n🔄 Trying alternative approach...');
        
        try {
            // Try getting portfolio
            const portfolioResponse = await axios.get(
                `https://solana-gateway.moralis.io/account/mainnet/${WALLET_ADDRESS}/portfolio`,
                moralisConfig
            );
            
            console.log('📊 Portfolio Data:');
            console.log(JSON.stringify(portfolioResponse.data, null, 2));
            
        } catch (altError) {
            console.error('❌ Alternative approach also failed:', altError.response?.data || altError.message);
        }
    }
}

// Run the analysis
getWalletInfo();
