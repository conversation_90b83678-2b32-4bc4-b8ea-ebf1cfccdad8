const axios = require('axios');

const MORALIS_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJub25jZSI6ImNjM2NlNjMxLTU3MmYtNDY4NC1hZTYwLTE3Y2Y1YmMyZTAyZSIsIm9yZ0lkIjoiNDYyMzg4IiwidXNlcklkIjoiNDc1Njk4IiwidHlwZUlkIjoiZDc3Zjg4NTctZTA4Zi00M2I3LWIzMGEtZDI3OGRkNmM0NTNkIiwidHlwZSI6IlBST0pFQ1QiLCJpYXQiOjE3NTM4ODE2MDAsImV4cCI6NDkwOTY0MTYwMH0.gmNIVS5dgAnOscm2CLKQstXxPvj4tK_zK1IeDGze-Co';
const WALLET_ADDRESS = 'Fn3UVQzeA5XDJXvVPnoZnYiUSEmBkU5MAzM3YeU54Mhu';

const moralisConfig = {
    headers: {
        'X-API-Key': MORALIS_API_KEY,
        'Content-Type': 'application/json'
    }
};

async function getTransactionHistory() {
    console.log(`\n📈 Getting transaction history for: ${WALLET_ADDRESS}\n`);
    
    try {
        // Try different endpoints for transaction history
        const endpoints = [
            `https://solana-gateway.moralis.io/account/mainnet/${WALLET_ADDRESS}/transactions`,
            `https://deep-index.moralis.io/api/v2.2/wallets/${WALLET_ADDRESS}/history?chain=solana`,
            `https://deep-index.moralis.io/api/v2.2/wallets/${WALLET_ADDRESS}/transactions?chain=solana`
        ];
        
        for (let i = 0; i < endpoints.length; i++) {
            console.log(`🔄 Trying endpoint ${i + 1}...`);
            try {
                const response = await axios.get(endpoints[i], moralisConfig);
                console.log(`✅ Success with endpoint ${i + 1}!`);
                console.log('📋 Transaction Data:');
                console.log(JSON.stringify(response.data, null, 2));
                return;
            } catch (error) {
                console.log(`❌ Endpoint ${i + 1} failed:`, error.response?.data?.message || error.message);
            }
        }
        
        console.log('\n⚠️  All transaction endpoints failed. This might be due to:');
        console.log('   - API limitations for transaction history');
        console.log('   - Wallet having no recent transactions');
        console.log('   - Different API structure for Solana transactions');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

// Run the transaction analysis
getTransactionHistory();
