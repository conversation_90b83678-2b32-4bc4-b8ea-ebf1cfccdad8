const { spawn } = require('child_process');

const TOKEN_ADDRESS = 'Aj27AMdcxtKvmuhbM2D6wcSWmFGCo8bST7g5BYDNBAGS';

class DebugTokenChecker {
    constructor() {
        this.mcpProcess = null;
        this.requestId = 1;
    }

    async startMCPServer() {
        this.mcpProcess = spawn('cmd', ['/c', 'npx', '@moralisweb3/api-mcp-server', '--transport', 'stdio'], {
            env: {
                ...process.env,
                MORALIS_API_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJub25jZSI6ImNjM2NlNjMxLTU3MmYtNDY4NC1hZTYwLTE3Y2Y1YmMyZTAyZSIsIm9yZ0lkIjoiNDYyMzg4IiwidXNlcklkIjoiNDc1Njk4IiwidHlwZUlkIjoiZDc3Zjg4NTctZTA4Zi00M2I3LWIzMGEtZDI3OGRkNmM0NTNkIiwidHlwZSI6IlBST0pFQ1QiLCJpYXQiOjE3NTM4ODE2MDAsImV4cCI6NDkwOTY0MTYwMH0.gmNIVS5dgAnOscm2CLKQstXxPvj4tK_zK1IeDGze-Co'
            },
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true
        });

        this.mcpProcess.stderr.on('data', () => {});
        await new Promise((resolve) => setTimeout(resolve, 2000));
    }

    async sendMCPRequest(method, params = {}) {
        return new Promise((resolve) => {
            const request = {
                jsonrpc: '2.0',
                id: this.requestId++,
                method: method,
                params: params
            };

            this.mcpProcess.stdin.write(JSON.stringify(request) + '\n');

            let responseData = '';
            const timeout = setTimeout(() => {
                resolve({ error: 'timeout', data: responseData });
            }, 15000);

            const onData = (data) => {
                responseData += data.toString();
                
                // Try to find complete JSON response
                const lines = responseData.split('\n');
                for (const line of lines) {
                    if (line.trim().startsWith('{') && line.includes('"result"')) {
                        clearTimeout(timeout);
                        try {
                            const response = JSON.parse(line.trim());
                            resolve(response.result);
                            this.mcpProcess.stdout.off('data', onData);
                            return;
                        } catch (e) {
                            // Continue looking
                        }
                    }
                }
            };

            this.mcpProcess.stdout.on('data', onData);
        });
    }

    async checkToken() {
        try {
            await this.startMCPServer();
            
            // First try to get token metadata to verify it exists
            const metadataResult = await this.sendMCPRequest('tools/call', {
                name: 'solana_gettokenmetadata',
                arguments: {
                    network: 'mainnet',
                    address: TOKEN_ADDRESS
                }
            });
            
            if (metadataResult && metadataResult.content) {
                const metadata = JSON.parse(metadataResult.content[0].text);
                console.log(`Token found: ${metadata.name} (${metadata.symbol})`);
            }
            
            // Now try to get swaps
            const swapsResult = await this.sendMCPRequest('tools/call', {
                name: 'solana_getswapsbytokenaddress',
                arguments: {
                    network: 'mainnet',
                    address: TOKEN_ADDRESS,
                    limit: 100
                }
            });
            
            if (swapsResult && swapsResult.content) {
                const data = JSON.parse(swapsResult.content[0].text);
                
                if (data.result && Array.isArray(data.result)) {
                    // Filter for last 24 hours and $5k+ purchases
                    const now = new Date();
                    const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                    
                    const recentLargeBuys = data.result.filter(swap => {
                        const swapTime = new Date(swap.blockTimestamp);
                        return swapTime >= twentyFourHoursAgo && 
                               swap.transactionType === 'buy' &&
                               parseFloat(swap.totalValueUsd || 0) >= 5000;
                    });
                    
                    const uniqueWallets = new Set(recentLargeBuys.map(buy => buy.walletAddress));
                    return uniqueWallets.size;
                } else {
                    return 0;
                }
            } else {
                return "No swap data available";
            }
            
        } catch (error) {
            return `Error: ${error.message}`;
        } finally {
            if (this.mcpProcess) {
                this.mcpProcess.kill();
            }
        }
    }
}

async function main() {
    const checker = new DebugTokenChecker();
    const result = await checker.checkToken();
    console.log(`Answer: ${result} wallets with $5k+ purchases in the last 24 hours`);
}

main().catch(console.error);
