const { spawn } = require('child_process');

const TOKEN_ADDRESS = 'Aj27AMdcxtKvmuhbM2D6wcSWmFGCo8bST7g5BYDNBAGS';

class SimpleTokenChecker {
    constructor() {
        this.mcpProcess = null;
        this.requestId = 1;
    }

    async startMCPServer() {
        this.mcpProcess = spawn('cmd', ['/c', 'npx', '@moralisweb3/api-mcp-server', '--transport', 'stdio'], {
            env: {
                ...process.env,
                MORALIS_API_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJub25jZSI6ImNjM2NlNjMxLTU3MmYtNDY4NC1hZTYwLTE3Y2Y1YmMyZTAyZSIsIm9yZ0lkIjoiNDYyMzg4IiwidXNlcklkIjoiNDc1Njk4IiwidHlwZUlkIjoiZDc3Zjg4NTctZTA4Zi00M2I3LWIzMGEtZDI3OGRkNmM0NTNkIiwidHlwZSI6IlBST0pFQ1QiLCJpYXQiOjE3NTM4ODE2MDAsImV4cCI6NDkwOTY0MTYwMH0.gmNIVS5dgAnOscm2CLKQstXxPvj4tK_zK1IeDGze-Co'
            },
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true
        });

        this.mcpProcess.stderr.on('data', () => {});
        await new Promise((resolve) => setTimeout(resolve, 2000));
    }

    async sendMCPRequest(method, params = {}) {
        return new Promise((resolve, reject) => {
            const request = {
                jsonrpc: '2.0',
                id: this.requestId++,
                method: method,
                params: params
            };

            this.mcpProcess.stdin.write(JSON.stringify(request) + '\n');

            const timeout = setTimeout(() => {
                reject(new Error('Request timeout'));
            }, 15000);

            const onData = (data) => {
                clearTimeout(timeout);
                try {
                    const response = JSON.parse(data.toString());
                    resolve(response.result);
                } catch (error) {
                    resolve({ rawData: data.toString() });
                }
                this.mcpProcess.stdout.off('data', onData);
            };

            this.mcpProcess.stdout.on('data', onData);
        });
    }

    async checkToken() {
        try {
            await this.startMCPServer();
            
            // Try to get token swaps with proper date filtering
            const fromDate = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
            
            const result = await this.sendMCPRequest('tools/call', {
                name: 'solana_getswapsbytokenaddress',
                arguments: {
                    network: 'mainnet',
                    address: TOKEN_ADDRESS,
                    fromDate: fromDate,
                    limit: 1000,
                    transactionTypes: 'buy'
                }
            });
            
            if (result && result.content && result.content[0]) {
                const data = JSON.parse(result.content[0].text);
                
                if (data.result && Array.isArray(data.result)) {
                    const whaleWallets = new Set();
                    
                    for (const swap of data.result) {
                        const usdAmount = parseFloat(swap.totalValueUsd || 0);
                        if (usdAmount >= 5000) {
                            whaleWallets.add(swap.walletAddress);
                        }
                    }
                    
                    return whaleWallets.size;
                } else {
                    return "No swap data found";
                }
            } else {
                return "Could not retrieve data";
            }
            
        } catch (error) {
            return `Error: ${error.message}`;
        } finally {
            if (this.mcpProcess) {
                this.mcpProcess.kill();
            }
        }
    }
}

async function main() {
    const checker = new SimpleTokenChecker();
    const result = await checker.checkToken();
    console.log(result);
}

main().catch(console.error);
