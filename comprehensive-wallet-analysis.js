const { Connection, PublicKey } = require('@solana/web3.js');
const axios = require('axios');

const WALLET_ADDRESS = 'Fn3UVQzeA5XDJXvVPnoZnYiUSEmBkU5MAzM3YeU54Mhu';

// Multiple RPC endpoints to try
const RPC_ENDPOINTS = [
    'https://api.mainnet-beta.solana.com',
    'https://solana-api.projectserum.com',
    'https://rpc.ankr.com/solana',
    'https://solana.public-rpc.com'
];

class SolanaWalletAnalyzer {
    constructor() {
        this.connection = null;
        this.walletPubkey = new PublicKey(WALLET_ADDRESS);
    }

    async initializeConnection() {
        for (const endpoint of RPC_ENDPOINTS) {
            try {
                console.log(`🔄 Trying RPC endpoint: ${endpoint}`);
                this.connection = new Connection(endpoint, 'confirmed');
                
                // Test the connection
                const version = await this.connection.getVersion();
                console.log(`✅ Connected to Solana RPC: ${endpoint}`);
                console.log(`   Solana version: ${version['solana-core']}`);
                return true;
            } catch (error) {
                console.log(`❌ Failed to connect to ${endpoint}: ${error.message}`);
            }
        }
        throw new Error('Failed to connect to any Solana RPC endpoint');
    }

    async getTransactionSignatures(limit = 20) {
        try {
            console.log(`\n📋 Getting last ${limit} transaction signatures...`);
            
            const signatures = await this.connection.getSignaturesForAddress(
                this.walletPubkey,
                { limit }
            );
            
            console.log(`✅ Found ${signatures.length} transactions`);
            return signatures;
        } catch (error) {
            console.error('❌ Error getting signatures:', error.message);
            return [];
        }
    }

    async analyzeTransaction(signature) {
        try {
            const tx = await this.connection.getTransaction(signature.signature, {
                maxSupportedTransactionVersion: 0
            });
            
            if (!tx) return null;

            const analysis = {
                signature: signature.signature,
                blockTime: signature.blockTime,
                slot: signature.slot,
                fee: tx.meta?.fee || 0,
                success: !signature.err,
                error: signature.err,
                accounts: tx.transaction.message.accountKeys.map(key => key.toString()),
                instructions: tx.transaction.message.instructions.length,
                logMessages: tx.meta?.logMessages || [],
                preBalances: tx.meta?.preBalances || [],
                postBalances: tx.meta?.postBalances || [],
                preTokenBalances: tx.meta?.preTokenBalances || [],
                postTokenBalances: tx.meta?.postTokenBalances || []
            };

            // Try to identify if this is a trade/swap
            analysis.isSwap = this.identifySwap(analysis);
            analysis.tradeInfo = this.extractTradeInfo(analysis);

            return analysis;
        } catch (error) {
            console.error(`❌ Error analyzing transaction ${signature.signature}:`, error.message);
            return null;
        }
    }

    identifySwap(analysis) {
        const logs = analysis.logMessages.join(' ').toLowerCase();
        const swapIndicators = [
            'swap', 'trade', 'exchange', 'jupiter', 'raydium', 'orca', 
            'serum', 'dex', 'amm', 'liquidity'
        ];
        
        return swapIndicators.some(indicator => logs.includes(indicator));
    }

    extractTradeInfo(analysis) {
        const tradeInfo = {
            type: 'unknown',
            tokens: [],
            amounts: []
        };

        // Analyze token balance changes
        const preTokens = analysis.preTokenBalances || [];
        const postTokens = analysis.postTokenBalances || [];

        // Create maps for easier comparison
        const preMap = new Map();
        const postMap = new Map();

        preTokens.forEach(token => {
            preMap.set(token.mint, token.uiTokenAmount.uiAmount || 0);
        });

        postTokens.forEach(token => {
            postMap.set(token.mint, token.uiTokenAmount.uiAmount || 0);
        });

        // Find tokens with balance changes
        const allMints = new Set([...preMap.keys(), ...postMap.keys()]);
        
        allMints.forEach(mint => {
            const preBal = preMap.get(mint) || 0;
            const postBal = postMap.get(mint) || 0;
            const change = postBal - preBal;
            
            if (Math.abs(change) > 0.000001) { // Ignore dust
                tradeInfo.tokens.push({
                    mint,
                    change,
                    direction: change > 0 ? 'received' : 'sent'
                });
            }
        });

        return tradeInfo;
    }

    async getComprehensiveAnalysis() {
        console.log(`\n🔍 Comprehensive Analysis for Wallet: ${WALLET_ADDRESS}\n`);
        
        try {
            await this.initializeConnection();
            
            // Get transaction signatures
            const signatures = await this.getTransactionSignatures(20);
            
            if (signatures.length === 0) {
                console.log('⚠️  No transactions found for this wallet');
                return;
            }

            console.log(`\n📊 Analyzing ${signatures.length} transactions...\n`);
            
            const trades = [];
            const allTransactions = [];

            for (let i = 0; i < signatures.length; i++) {
                console.log(`🔄 Analyzing transaction ${i + 1}/${signatures.length}...`);
                
                const analysis = await this.analyzeTransaction(signatures[i]);
                if (analysis) {
                    allTransactions.push(analysis);
                    if (analysis.isSwap) {
                        trades.push(analysis);
                    }
                }
                
                // Add small delay to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Display results
            this.displayResults(allTransactions, trades);
            
        } catch (error) {
            console.error('❌ Analysis failed:', error.message);
        }
    }

    displayResults(allTransactions, trades) {
        console.log('\n' + '='.repeat(80));
        console.log('📈 TRANSACTION ANALYSIS RESULTS');
        console.log('='.repeat(80));

        console.log(`\n📊 Summary:`);
        console.log(`   Total Transactions: ${allTransactions.length}`);
        console.log(`   Identified Trades/Swaps: ${trades.length}`);
        console.log(`   Other Transactions: ${allTransactions.length - trades.length}`);

        console.log(`\n🔥 LAST 20 TRANSACTIONS:`);
        console.log('-'.repeat(80));

        allTransactions.forEach((tx, index) => {
            const date = tx.blockTime ? new Date(tx.blockTime * 1000).toLocaleString() : 'Unknown';
            const status = tx.success ? '✅' : '❌';
            const type = tx.isSwap ? '🔄 SWAP' : '📝 TX';
            
            console.log(`\n${index + 1}. ${type} ${status} ${tx.signature.substring(0, 20)}...`);
            console.log(`   Date: ${date}`);
            console.log(`   Fee: ${(tx.fee / 1e9).toFixed(6)} SOL`);
            console.log(`   Instructions: ${tx.instructions}`);
            
            if (tx.tradeInfo && tx.tradeInfo.tokens.length > 0) {
                console.log(`   Token Changes:`);
                tx.tradeInfo.tokens.forEach(token => {
                    const changeStr = token.change > 0 ? `+${token.change}` : `${token.change}`;
                    console.log(`     ${token.direction.toUpperCase()}: ${changeStr} (${token.mint.substring(0, 20)}...)`);
                });
            }
        });

        if (trades.length > 0) {
            console.log(`\n🎯 IDENTIFIED TRADES (${trades.length}):`);
            console.log('-'.repeat(80));
            
            trades.forEach((trade, index) => {
                const date = trade.blockTime ? new Date(trade.blockTime * 1000).toLocaleString() : 'Unknown';
                console.log(`\n${index + 1}. TRADE - ${trade.signature.substring(0, 30)}...`);
                console.log(`   Date: ${date}`);
                console.log(`   Fee: ${(trade.fee / 1e9).toFixed(6)} SOL`);
                
                if (trade.tradeInfo.tokens.length > 0) {
                    console.log(`   Tokens Involved:`);
                    trade.tradeInfo.tokens.forEach(token => {
                        const changeStr = token.change > 0 ? `+${token.change}` : `${token.change}`;
                        console.log(`     ${token.direction}: ${changeStr} ${token.mint.substring(0, 30)}...`);
                    });
                }
            });
        }
    }
}

// Run the analysis
async function main() {
    const analyzer = new SolanaWalletAnalyzer();
    await analyzer.getComprehensiveAnalysis();
}

main().catch(console.error);
