const { spawn } = require('child_process');
const readline = require('readline');

const WALLET_ADDRESS = 'Fn3UVQzeA5XDJXvVPnoZnYiUSEmBkU5MAzM3YeU54Mhu';

class MoralisMCPClient {
    constructor() {
        this.mcpProcess = null;
        this.requestId = 1;
    }

    async startMCPServer() {
        console.log('🚀 Starting Moralis MCP Server...');

        // Use cmd on Windows to run npx
        this.mcpProcess = spawn('cmd', ['/c', 'npx', '@moralisweb3/api-mcp-server', '--transport', 'stdio'], {
            env: {
                ...process.env,
                MORALIS_API_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJub25jZSI6ImNjM2NlNjMxLTU3MmYtNDY4NC1hZTYwLTE3Y2Y1YmMyZTAyZSIsIm9yZ0lkIjoiNDYyMzg4IiwidXNlcklkIjoiNDc1Njk4IiwidHlwZUlkIjoiZDc3Zjg4NTctZTA4Zi00M2I3LWIzMGEtZDI3OGRkNmM0NTNkIiwidHlwZSI6IlBST0pFQ1QiLCJpYXQiOjE3NTM4ODE2MDAsImV4cCI6NDkwOTY0MTYwMH0.gmNIVS5dgAnOscm2CLKQstXxPvj4tK_zK1IeDGze-Co'
            },
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true
        });

        this.mcpProcess.stderr.on('data', (data) => {
            console.log('MCP Server:', data.toString());
        });

        // Wait for server to be ready
        await new Promise((resolve) => {
            setTimeout(resolve, 2000);
        });

        console.log('✅ MCP Server started');
    }

    async sendMCPRequest(method, params = {}) {
        return new Promise((resolve, reject) => {
            const request = {
                jsonrpc: '2.0',
                id: this.requestId++,
                method: method,
                params: params
            };

            console.log(`📤 Sending MCP request: ${method}`);
            console.log('Request:', JSON.stringify(request, null, 2));

            this.mcpProcess.stdin.write(JSON.stringify(request) + '\n');

            const timeout = setTimeout(() => {
                reject(new Error('Request timeout'));
            }, 30000);

            const onData = (data) => {
                clearTimeout(timeout);
                try {
                    const response = JSON.parse(data.toString());
                    console.log(`📥 MCP response for ${method}:`);
                    console.log(JSON.stringify(response, null, 2));
                    
                    if (response.error) {
                        reject(new Error(response.error.message));
                    } else {
                        resolve(response.result);
                    }
                } catch (error) {
                    console.log('Raw response:', data.toString());
                    reject(error);
                }
                this.mcpProcess.stdout.off('data', onData);
            };

            this.mcpProcess.stdout.on('data', onData);
        });
    }

    async listAvailableTools() {
        try {
            console.log('\n🔧 Getting available MCP tools...');
            const tools = await this.sendMCPRequest('tools/list');
            
            console.log('\n📋 Available Moralis MCP Tools:');
            tools.tools.forEach((tool, index) => {
                console.log(`${index + 1}. ${tool.name}`);
                console.log(`   Description: ${tool.description}`);
                console.log('');
            });
            
            return tools.tools;
        } catch (error) {
            console.error('❌ Error listing tools:', error.message);
            return [];
        }
    }

    async getWalletTransactions() {
        try {
            console.log(`\n💰 Getting wallet swap transactions for: ${WALLET_ADDRESS}`);

            const result = await this.sendMCPRequest('tools/call', {
                name: 'solana_getswapsbywalletaddress',
                arguments: {
                    network: 'mainnet',
                    address: WALLET_ADDRESS,
                    limit: 20
                }
            });

            console.log(`✅ Success getting swap transactions`);
            return result;

        } catch (error) {
            console.error('❌ Error getting transactions:', error.message);
            return null;
        }
    }

    async getWalletPortfolio() {
        try {
            console.log(`\n🪙 Getting wallet portfolio for: ${WALLET_ADDRESS}`);

            const result = await this.sendMCPRequest('tools/call', {
                name: 'solana_getportfolio',
                arguments: {
                    network: 'mainnet',
                    address: WALLET_ADDRESS,
                    excludeSpam: false
                }
            });

            return result;

        } catch (error) {
            console.error('❌ Error getting portfolio:', error.message);
            return null;
        }
    }

    async getWalletTokens() {
        try {
            console.log(`\n🪙 Getting wallet SPL tokens for: ${WALLET_ADDRESS}`);

            const result = await this.sendMCPRequest('tools/call', {
                name: 'solana_getspl',
                arguments: {
                    network: 'mainnet',
                    address: WALLET_ADDRESS,
                    excludeSpam: false
                }
            });

            return result;

        } catch (error) {
            console.error('❌ Error getting SPL tokens:', error.message);
            return null;
        }
    }

    async analyzeWallet() {
        try {
            await this.startMCPServer();
            
            console.log('\n' + '='.repeat(80));
            console.log('🔍 MORALIS MCP WALLET ANALYSIS');
            console.log('='.repeat(80));
            console.log(`Wallet: ${WALLET_ADDRESS}`);
            
            // Get wallet portfolio (includes tokens and balance)
            const portfolio = await this.getWalletPortfolio();
            if (portfolio) {
                console.log('\n💼 WALLET PORTFOLIO:');
                this.displayPortfolio(portfolio);
            }

            // Get detailed SPL tokens
            const tokens = await this.getWalletTokens();
            if (tokens) {
                console.log('\n🪙 SPL TOKENS:');
                this.displayTokens(tokens);
            }

            // Get swap transactions (trades)
            const transactions = await this.getWalletTransactions();
            if (transactions) {
                console.log('\n📈 RECENT TRADES:');
                this.displayTransactions(transactions);
            }
            
        } catch (error) {
            console.error('❌ Analysis failed:', error.message);
        } finally {
            if (this.mcpProcess) {
                this.mcpProcess.kill();
                console.log('\n🛑 MCP Server stopped');
            }
        }
    }

    displayPortfolio(portfolio) {
        if (portfolio.content && portfolio.content[0] && portfolio.content[0].text) {
            try {
                const data = JSON.parse(portfolio.content[0].text);

                console.log(`💰 SOL Balance: ${data.nativeBalance?.solana || 'Unknown'} SOL`);

                if (data.tokens && data.tokens.length > 0) {
                    console.log(`\n🏆 TOP 10 TOKENS:`);
                    data.tokens.slice(0, 10).forEach((token, index) => {
                        console.log(`${index + 1}. ${token.symbol || 'Unknown'} (${token.name || 'Unknown'})`);
                        console.log(`   Amount: ${token.amount || 'Unknown'}`);
                        console.log(`   Mint: ${token.mint}`);
                        console.log(`   Verified: ${token.isVerifiedContract ? '✅' : '❌'}`);
                        console.log('');
                    });
                }
            } catch (error) {
                console.log('Raw portfolio data:', portfolio.content[0].text);
            }
        } else {
            console.log('Portfolio data:', JSON.stringify(portfolio, null, 2));
        }
    }

    displayTokens(tokens) {
        if (tokens.content && tokens.content[0] && tokens.content[0].text) {
            try {
                const data = JSON.parse(tokens.content[0].text);
                console.log(`Found ${data.length || 0} SPL tokens`);

                if (data && data.length > 0) {
                    data.slice(0, 10).forEach((token, index) => {
                        console.log(`${index + 1}. ${token.symbol || 'Unknown'}`);
                        console.log(`   Amount: ${token.amount || 'Unknown'}`);
                        console.log(`   Mint: ${token.mint}`);
                        console.log('');
                    });
                }
            } catch (error) {
                console.log('Raw token data:', tokens.content[0].text);
            }
        } else {
            console.log('Token data:', JSON.stringify(tokens, null, 2));
        }
    }

    displayTransactions(transactions) {
        if (transactions.content && transactions.content[0] && transactions.content[0].text) {
            try {
                const data = JSON.parse(transactions.content[0].text);
                console.log(`Found ${data.length || 0} recent trades`);

                if (data && data.length > 0) {
                    data.slice(0, 20).forEach((tx, index) => {
                        console.log(`${index + 1}. ${tx.transactionType?.toUpperCase() || 'TRADE'} - ${new Date(tx.blockTime * 1000).toLocaleString()}`);
                        console.log(`   Signature: ${tx.signature?.substring(0, 30)}...`);
                        console.log(`   Token: ${tx.tokenSymbol || 'Unknown'} (${tx.tokenAddress?.substring(0, 20)}...)`);
                        console.log(`   Amount: ${tx.tokenAmount || 'Unknown'}`);
                        console.log(`   Price: $${tx.priceUsd || 'Unknown'}`);
                        console.log(`   SOL Amount: ${tx.solAmount || 'Unknown'} SOL`);
                        console.log('');
                    });
                }
            } catch (error) {
                console.log('Raw transaction data:', transactions.content[0].text);
            }
        } else {
            console.log('Transaction data:', JSON.stringify(transactions, null, 2));
        }
    }
}

// Run the analysis
async function main() {
    const client = new MoralisMCPClient();
    await client.analyzeWallet();
}

main().catch(console.error);
